{"name": "iterms-h5", "version": "0.0.1", "license": "MIT", "engines": {"node": ">= 18", "pnpm": ">= 9"}, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check build-only", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint:eslint": "eslint . --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "release": "standard-version", "prepare": "husky install"}, "dependencies": {"axios": "^1.7.7", "clsx": "^2.1.1", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "pinia": "^2.2.5", "tailwind-merge": "^2.5.4", "vant": "^4.9.8", "vue": "^3.5.12", "vue-router": "^4.4.5"}, "devDependencies": {"@commitlint/config-conventional": "^19.5.0", "@eslint/js": "^9.14.0", "@iconify-icons/fa6-solid": "^1.2.13", "@iconify/vue": "^4.1.2", "@rushstack/eslint-patch": "^1.10.4", "@types/node": "^22.8.7", "@types/nprogress": "^0.2.3", "@typescript-eslint/eslint-plugin": "^8.12.2", "@typescript-eslint/parser": "^8.12.2", "@vitejs/plugin-vue": "^5.1.4", "@vitejs/plugin-vue-jsx": "^4.0.1", "@vue/eslint-config-typescript": "^14.1.3", "@vue/tsconfig": "^0.5.1", "cnjm-postcss-px-to-viewport": "^1.0.1", "commitlint": "^19.5.0", "eslint": "^9.14.0", "eslint-config-prettier": "^9.1.0", "eslint-define-config": "^2.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.30.0", "globals": "^15.11.0", "husky": "^9.1.6", "less": "^4.2.0", "mockjs": "^1.1.0", "npm-run-all": "^4.1.5", "postcss": "^8.4.47", "prettier": "^3.3.3", "sass": "^1.89.2", "standard-version": "^9.5.0", "typescript": "~5.6.3", "typescript-eslint": "^8.12.2", "unplugin-vue-components": "^0.27.4", "vite": "^5.4.10", "vite-plugin-cdn2": "^1.1.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.2", "vite-plugin-mock-dev-server": "^1.8.0", "vite-plugin-svg-icons": "^2.0.1", "vue-eslint-parser": "^9.4.3", "vue-tsc": "^2.1.10"}, "pnpm": {"peerDependencyRules": {"ignoreMissing": ["rollup"], "allowedVersions": {"eslint": "9"}}}}