{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "bundler", "strict": false, "jsx": "preserve", "importHelpers": true, "experimentalDecorators": true, "strictFunctionTypes": false, "skipLibCheck": true, "esModuleInterop": true, "isolatedModules": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "sourceMap": true, "baseUrl": ".", "allowJs": false, "resolveJsonModule": true, "lib": ["ESNext", "DOM"], "paths": {"@/*": ["./src/*"], "@build/*": ["./build/*"]}, "types": ["node", "vite/client"]}, "include": ["mock/*.ts", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "typings/*.d.ts", "vite.config.ts"], "exclude": ["dist", "**/*.js", "node_modules"]}