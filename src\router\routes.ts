import Layout from "@/layout/index.vue"
import type { RouteRecordRaw } from "vue-router"
import Demo from "@/views/demo/index.vue"

const routes: Array<RouteRecordRaw> = [
  {
    path: "/",
    name: "root",
    component: Layout,
    redirect: { name: "<PERSON><PERSON>" },
    children: [
      {
        path: "demo",
        name: "De<PERSON>",
        component: Demo,
        meta: {
          title: "主页"
        }
      },
      {
        path: "tools",
        name: "Tools",
        component: () => import("@/views/tools/index.vue"),
        meta: {
          title: "工具"
        }
      },
      {
        path: "about",
        name: "About",
        component: () => import("@/views/about/index.vue"),
        meta: {
          title: "关于",
          noCache: true
        }
      }
    ]
  },
  // 新增不包含 Layout 的路由
  {
    path: "/about-detail",
    name: "AboutDetail",
    component: () => import("@/views/about/detail.vue"),
    meta: {
      title: "关于详情",
      noCache: true
    }
  }
]

export default routes
