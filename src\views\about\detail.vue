<template>
  <div class="contract-review">
    <van-nav-bar title="合同审查" left-arrow @click-left="goBack" />

    <!-- 风险等级 Tabs -->
    <div class="risk-tabs">
      <van-tabs v-model:active="activeTab" type="card" shrink>
        <van-tab
          v-for="(tab, index) in riskTabs"
          :key="index"
          :title="tab.title"
          :name="tab.name"
        >
          <div class="tab-content">
            <!-- 审查项目列表 -->
            <div class="review-items">
              <van-collapse v-model="activeCollapse" accordion>
                <van-collapse-item
                  v-for="(item, index) in reviewItems"
                  :key="index"
                  :title="item.title"
                  :name="index"
                >
                  <div class="collapse-content">
                    <div v-if="item.content" class="item-content">
                      {{ item.content }}
                    </div>
                    <div
                      v-if="item.details && item.details.length > 0"
                      class="item-details"
                    >
                      <div
                        v-for="(detail, detailIndex) in item.details"
                        :key="detailIndex"
                        class="detail-item"
                      >
                        <div class="detail-title">{{ detail.title }}</div>
                        <div class="detail-desc">{{ detail.description }}</div>
                      </div>
                    </div>
                  </div>
                </van-collapse-item>
              </van-collapse>
            </div>
          </div>
        </van-tab>
      </van-tabs>
    </div>

    <!-- 底部查看原文按钮 -->
    <div class="bottom-action">
      <van-button type="primary" block round @click="viewOriginal">
        查看原文
      </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { useRouter } from "vue-router";
import { showToast } from "vant";

defineOptions({
  name: "AboutDetail"
});

const router = useRouter();

// 当前激活的 tab
const activeTab = ref("high");

// 当前展开的折叠面板
const activeCollapse = ref<number | null>(null);

// 风险等级 tabs
const riskTabs = reactive([
  { title: "高风险 10", name: "high" },
  { title: "中风险 10", name: "medium" },
  { title: "低风险 10", name: "low" },
  { title: "无风险", name: "none" }
]);

// 审查项目数据
const reviewItems = reactive([
  {
    title: "劳动报酬与调整机制审查",
    content: "审查劳动报酬的构成、支付方式、调整机制等相关条款。",
    details: [
      {
        title: "基本工资标准",
        description: "检查基本工资是否符合当地最低工资标准，工资构成是否明确。"
      },
      {
        title: "绩效奖金机制",
        description: "审查绩效考核标准、奖金计算方式及发放时间等条款。"
      },
      {
        title: "工资调整条款",
        description: "检查工资调整的触发条件、调整幅度及调整周期等规定。"
      }
    ]
  },
  {
    title: "其他审查项",
    content: "包含其他重要的合同条款审查内容。",
    details: [
      {
        title: "保密条款",
        description: "审查保密义务的范围、期限及违约责任。"
      },
      {
        title: "竞业限制",
        description: "检查竞业限制的合理性、补偿标准及执行期限。"
      }
    ]
  },
  {
    title: "工作内容与地点变更审查",
    content: "审查工作内容、工作地点变更的相关条款。",
    details: [
      {
        title: "工作内容变更",
        description: "检查工作内容变更的程序、通知期限及员工同意机制。"
      },
      {
        title: "工作地点变更",
        description: "审查工作地点变更的合理距离、交通补贴及住宿安排。"
      }
    ]
  },
  {
    title: "社会保险与福利审查",
    content: "审查社会保险缴纳、福利待遇等相关条款。",
    details: [
      {
        title: "社保缴纳",
        description: "检查五险一金的缴纳基数、比例及缴纳时间。"
      },
      {
        title: "福利待遇",
        description: "审查年假、病假、产假等各类假期及福利待遇。"
      }
    ]
  },
  {
    title: "合同解除与终止审查",
    content: "审查合同解除、终止的条件和程序。",
    details: [
      {
        title: "解除条件",
        description: "检查合同解除的法定条件及约定条件的合法性。"
      },
      {
        title: "经济补偿",
        description: "审查经济补偿金的计算标准及支付时间。"
      }
    ]
  },
  {
    title: "劳动保护与劳动条件审查",
    content: "审查劳动保护措施、工作环境等条款。",
    details: [
      {
        title: "安全保护",
        description: "检查劳动安全保护措施、职业病防护等规定。"
      },
      {
        title: "工作时间",
        description: "审查工作时间安排、加班制度及休息休假制度。"
      }
    ]
  },
  {
    title: "合同变更与补充审查",
    content: "审查合同变更、补充的程序和效力。",
    details: [
      {
        title: "变更程序",
        description: "检查合同变更的协商程序、书面确认要求。"
      },
      {
        title: "补充协议",
        description: "审查补充协议的效力及与原合同的关系。"
      }
    ]
  },
  {
    title: "合同主体与签署审查",
    content: "审查合同主体资格、签署程序等。",
    details: [
      {
        title: "主体资格",
        description: "检查用人单位的主体资格、营业执照等证照。"
      },
      {
        title: "签署程序",
        description: "审查合同签署的程序、见证人及生效条件。"
      }
    ]
  }
]);

const goBack = () => {
  router.back();
};

const viewOriginal = () => {
  showToast("查看原文功能");
};
</script>

<style scoped lang="scss">
.contract-review {
  min-height: 100vh;
  background: var(--van-background-color);
  display: flex;
  flex-direction: column;

  :deep(.van-nav-bar__left .van-icon) {
    color: rgba(0, 0, 0, 0.9);
    font-size: 18px;
  }
}

.risk-tabs {
  flex: 1;
  padding: 0 12px;

  :deep(.van-tabs__nav) {
    background: transparent;
    margin-bottom: 16px;
  }

  :deep(.van-tab) {
    font-size: 14px;
    padding: 8px 12px;
    border-radius: 20px;
    margin: 0 4px;
    background: #f7f8fa;
    color: #646566;
    border: none;

    &.van-tab--active {
      background: #1989fa;
      color: white;
    }
  }

  :deep(.van-tabs__line) {
    display: none;
  }
}

.tab-content {
  padding-bottom: 80px; // 为底部按钮留出空间
}

.review-items {
  :deep(.van-collapse-item__title) {
    font-size: 16px;
    font-weight: 500;
    color: #323233;
    padding: 16px 0;
  }

  :deep(.van-collapse-item__content) {
    padding: 0 0 16px 0;
    background: #f7f8fa;
    border-radius: 8px;
    margin-bottom: 8px;
  }

  :deep(.van-collapse-item__wrapper) {
    border: none;
    margin-bottom: 8px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  :deep(.van-collapse-item) {
    border: none;
    background: white;
  }

  :deep(.van-collapse-item__title::after) {
    border: none;
  }
}

.collapse-content {
  padding: 16px;
}

.item-content {
  font-size: 14px;
  color: #646566;
  line-height: 1.6;
  margin-bottom: 16px;
  padding: 12px;
  background: white;
  border-radius: 6px;
}

.item-details {
  .detail-item {
    margin-bottom: 12px;
    padding: 12px;
    background: white;
    border-radius: 6px;
    border-left: 3px solid #1989fa;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .detail-title {
    font-size: 14px;
    font-weight: 500;
    color: #323233;
    margin-bottom: 6px;
  }

  .detail-desc {
    font-size: 13px;
    color: #646566;
    line-height: 1.5;
  }
}

.bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px 20px;
  background: white;
  border-top: 1px solid #ebedf0;
  z-index: 100;

  :deep(.van-button) {
    height: 44px;
    font-size: 16px;
    font-weight: 500;
  }
}
</style>
