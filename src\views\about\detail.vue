<template>
  <div class="contract-review">
    <van-nav-bar title="合同审查" left-arrow @click-left="goBack" />
    <div class="wps-container">原文内容</div>
    <div class="tabs-container">
      <!-- 风险等级 Tabs -->
      <div>
        <van-tabs
          :swipe-threshold="3"
          title-active-color="#492ED1"
          :ellipsis="true"
          :shrink="false"
          v-model:active="activeTab"
        >
          <van-tab
            v-for="(tab, index) in riskTabs"
            :key="index"
            :title="tab.title"
            :name="tab.name"
          >
          </van-tab>
        </van-tabs>
      </div>

      <!-- 底部查看原文按钮 -->
      <div class="bottom-action">
        <van-button type="primary" block round @click="viewOriginal">
          查看原文
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { useRouter } from "vue-router";
import { showToast } from "vant";

defineOptions({
  name: "AboutDetail"
});

const router = useRouter();

// 当前激活的 tab
const activeTab = ref("high");

// 当前展开的折叠面板
const activeCollapse = ref<number | null>(null);

// 风险等级 tabs
const riskTabs = reactive([
  { title: "高风险 10", name: "high" },
  { title: "中风险 10", name: "medium" },
  { title: "低风险 10", name: "low" },
  { title: "无风险", name: "none" }
]);

// 审查项目数据
const reviewItems = reactive([
  {
    title: "劳动报酬与调整机制审查",
    content: "审查劳动报酬的构成、支付方式、调整机制等相关条款。",
    details: [
      {
        title: "基本工资标准",
        description: "检查基本工资是否符合当地最低工资标准，工资构成是否明确。"
      },
      {
        title: "绩效奖金机制",
        description: "审查绩效考核标准、奖金计算方式及发放时间等条款。"
      },
      {
        title: "工资调整条款",
        description: "检查工资调整的触发条件、调整幅度及调整周期等规定。"
      }
    ]
  },
  {
    title: "其他审查项",
    content: "包含其他重要的合同条款审查内容。",
    details: [
      {
        title: "保密条款",
        description: "审查保密义务的范围、期限及违约责任。"
      },
      {
        title: "竞业限制",
        description: "检查竞业限制的合理性、补偿标准及执行期限。"
      }
    ]
  },

  {
    title: "合同变更与补充审查",
    content: "审查合同变更、补充的程序和效力。",
    details: [
      {
        title: "变更程序",
        description: "检查合同变更的协商程序、书面确认要求。"
      },
      {
        title: "补充协议",
        description: "审查补充协议的效力及与原合同的关系。"
      }
    ]
  }
]);

const goBack = () => {
  router.back();
};

const viewOriginal = () => {
  showToast("查看原文功能");
};
</script>

<style scoped lang="scss">
.contract-review {
  min-height: 100vh;
  background: var(--van-background-color);
  display: flex;
  flex-direction: column;

  :deep(.van-nav-bar__left .van-icon) {
    color: rgba(0, 0, 0, 0.9);
    font-size: 18px;
  }
  .wps-container {
    border: 1px solid red;
  }
  .tabs-container {
    border: 1px solid blue;
    position: fixed;
    top: 46px;
    left: 0;
    height: calc(100vh - 46px);
    width: 100%;
    z-index: 3;
    // 设置底部横条颜色
    :deep(.van-tabs__line) {
      background-color: #492ed1;
    }
  }
}

.bottom-action {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px 20px;
  background: white;
  border-top: 1px solid #ebedf0;
  z-index: 100;

  :deep(.van-button) {
    height: 44px;
    font-size: 16px;
    font-weight: 500;
  }
}
</style>
