<template>
  <div class="about-detail">
    <van-nav-bar title="合同审查" left-arrow @click-left="goBack" />
    <div class="content">
      <h2>关于我们</h2>
      <p>这是一个详情页面，不包含底部 tabbar</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";

defineOptions({
  name: "AboutDetail"
});

const router = useRouter();

const goBack = () => {
  router.back();
};
</script>

<style scoped lang="scss">
.about-detail {
  min-height: 100vh;
  background: var(--van-background-color);

  :deep(.van-nav-bar__left .van-icon) {
    color: rgba(0, 0, 0, 0.9);
    font-size: 18px;
  }
}

.content {
  padding: 20px;

  h2 {
    margin-bottom: 16px;
    font-size: 20px;
    font-weight: bold;
  }

  p {
    line-height: 1.6;
    color: var(--van-text-color-2);
  }
}
</style>
