<template>
  <div class="about-page">
    <div class="about-item" @click="goToDetail">关于详情</div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";

defineOptions({
  name: "About"
});

const router = useRouter();

const goToDetail = () => {
  router.push({ name: "AboutDetail" });
};
</script>

<style scoped lang="scss">
.about-page {
  padding: 20px;
}

.about-item {
  padding: 16px;
  background: var(--color-block-background);
  border-radius: 8px;
  cursor: pointer;
  text-align: center;

  &:hover {
    opacity: 0.8;
  }
}
</style>
