# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## [0.7.0](https://github.com/yulimchen/vue3-h5-template/compare/v0.6.1...v0.7.0) (2024-12-01)


### 🐛 Bug Fixes | Bug 修复

* 修复黑暗模式初始化缺失类名问题 ([831599e](https://github.com/yulimchen/vue3-h5-template/commit/831599e162b09dbf0b00379d06499eb6e21b4bdf))


### ♻️ Code Refactoring | 代码重构

* **demo:** 调整 demo 页面样式 ([6424739](https://github.com/yulimchen/vue3-h5-template/commit/64247395eaa840056678ca81059614e636ff5743))


### 🚀 Chore | 构建/工程依赖/工具

* `composables` 目录命名 ([15de708](https://github.com/yulimchen/vue3-h5-template/commit/15de70893d9387a121c169f5cd639247719deac8))
* 文件命名调整 ([9aca455](https://github.com/yulimchen/vue3-h5-template/commit/9aca455cd184856bee05453100603c1bde27933b))


### ✏️ Documentation | 文档

* banner 图更换 ([f4e18e9](https://github.com/yulimchen/vue3-h5-template/commit/f4e18e90703dffd5d9fd879b4ce07baebde9da6f))

### [0.6.1](https://github.com/yulimchen/vue3-h5-template/compare/v0.6.0...v0.6.1) (2024-11-03)


### ✨ Features | 新功能

* 增加主题切换动画 ([930637d](https://github.com/yulimchen/vue3-h5-template/commit/930637dec8df057cd27f0a6eeb6970a3a6310a36))


### ♻️ Code Refactoring | 代码重构

* 使用 `defineOptions` 特性 ([33bc52a](https://github.com/yulimchen/vue3-h5-template/commit/33bc52ac227248332f0ca8c782ddc697aa1ce1a9))


### 🚀 Chore | 构建/工程依赖/工具

* **deps:** 依赖升级 ([cdbf143](https://github.com/yulimchen/vue3-h5-template/commit/cdbf143ec1f1424a708a2eb0c20b8a11bb3fdf1f))

## [0.6.0](https://github.com/yulimchen/vue3-h5-template/compare/v0.5.9...v0.6.0) (2024-06-27)


### ♻️ Code Refactoring | 代码重构

* 移除 CJS 配置 ([6754982](https://github.com/yulimchen/vue3-h5-template/commit/6754982edc374aeda9ac57e99698ef67b5be6397))


### ✨ Features | 新功能

* 集成 iconify 图标 ([d1c2611](https://github.com/yulimchen/vue3-h5-template/commit/d1c2611fc9a66fef5eef3d86a28ca32750ac8e7f))


### 🚀 Chore | 构建/工程依赖/工具

* 增加 `Iconify` 编辑器扩展推荐 ([747f504](https://github.com/yulimchen/vue3-h5-template/commit/747f504a0288ef1f12f1c054a0bd56488a4133ba))
* 调整视口适配转换为 vmin 单位 ([bb4ae78](https://github.com/yulimchen/vue3-h5-template/commit/bb4ae785e4857af6e164637b5b2fd44f9339efe7))


### ✏️ Documentation | 文档

* 修复文档图片路径 ([b22620d](https://github.com/yulimchen/vue3-h5-template/commit/b22620d50adec2e0cf3438e086c899474fa0bf30))
* 文档更新 ([9beee9b](https://github.com/yulimchen/vue3-h5-template/commit/9beee9bc2d6aa592f46ffef5a829fdecfc8a34db))
* 文档更新 ([9c53935](https://github.com/yulimchen/vue3-h5-template/commit/9c539350d6a079adec2ec6cc066867d413195311))
* 文档更新 ([2461454](https://github.com/yulimchen/vue3-h5-template/commit/24614543b8cdd5727681f81b3a49c9251fd132a0))
* 版权信息更新 ([b7fc912](https://github.com/yulimchen/vue3-h5-template/commit/b7fc912ad6acb18c131ff1d4cc3465377383419e))

### [0.5.9](https://github.com/yulimchen/vue3-h5-template/compare/v0.5.8...v0.5.9) (2024-05-19)


### ✏️ Documentation | 文档

* 文档更新 ([cf25514](https://github.com/yulimchen/vue3-h5-template/commit/cf255144f178882ff1be5de04edcd1e7c9bd1771))
* 文档更新 `i18n` 分支说明 ([9348d53](https://github.com/yulimchen/vue3-h5-template/commit/9348d53648654ee188819fba07e24596e64dc62d))


### 🐛 Bug Fixes | Bug 修复

* 升级 eslint9 相关配置 ([b28c5a7](https://github.com/yulimchen/vue3-h5-template/commit/b28c5a7ced4f5ae9430eb46c7cf601948a964648))


### ♻️ Code Refactoring | 代码重构

* 配置文件改为 ESM ([5353749](https://github.com/yulimchen/vue3-h5-template/commit/535374918cc30faec750c14c7effe795f960ebd3))


### 🚀 Chore | 构建/工程依赖/工具

* **deps:** 依赖升级 ([9288363](https://github.com/yulimchen/vue3-h5-template/commit/9288363a402d91e1b3041990dbaa24d7a2490101))
* VSCode 推荐扩展更新 ([1f60a68](https://github.com/yulimchen/vue3-h5-template/commit/1f60a68cca429e69172309c41e7a0f79d3fa4cfe))
* 格式化代码 ([306dc0d](https://github.com/yulimchen/vue3-h5-template/commit/306dc0daf5c1d2cb3c2d3c504509c304824dc31c))
* 编辑器环境 TypeScript 配置 ([a28e1e4](https://github.com/yulimchen/vue3-h5-template/commit/a28e1e48837cfdaadbfeca9ec456a1e3349a4950))

### [0.5.8](https://github.com/yulimchen/vue3-h5-template/compare/v0.5.7...v0.5.8) (2024-03-07)


### 🚀 Chore | 构建/工程依赖/工具

* 调整 `tsconfig` 配置 ([24eceb3](https://github.com/yulimchen/vue3-h5-template/commit/24eceb3d9fa5cdabc18b57e71521a8802125a69a))


### 🐛 Bug Fixes | Bug 修复

* 修复进度条设置父元素导致的样式问题 (fix [#52](https://github.com/yulimchen/vue3-h5-template/issues/52)) ([08310ca](https://github.com/yulimchen/vue3-h5-template/commit/08310ca1f8346939a448e8978c18ec9944660684))

### [0.5.7](https://github.com/yulimchen/vue3-h5-template/compare/v0.5.6...v0.5.7) (2023-12-03)


### 🐛 Bug Fixes | Bug 修复

* **router:** 默认根路径重定向大小写修复 ([ca8fa2d](https://github.com/yulimchen/vue3-h5-template/commit/ca8fa2d8da8e0bd43485aa510ee798ac01b4a65a))
* 修复打包报错 ([9306f02](https://github.com/yulimchen/vue3-h5-template/commit/9306f025d6eee0f182b0a1ad4f6ad7e97a5cdfc2))


### 🚀 Chore | 构建/工程依赖/工具

* 依赖升级 ([47196f8](https://github.com/yulimchen/vue3-h5-template/commit/47196f8e7f820caf8815998fb69032e6df58b893))
* 增加类型声明目录 ([2dc302b](https://github.com/yulimchen/vue3-h5-template/commit/2dc302b146567697225a4c40b1f50bcda7f42e87))


### ✏️ Documentation | 文档

* 文档更新 `js-version` 分支说明 ([db939f0](https://github.com/yulimchen/vue3-h5-template/commit/db939f01917c458276d94d5c220e9a70318cd3a8))


### ✨ Features | 新功能

* 支持生产环境打包 `cdn` 加速 ([93bbe72](https://github.com/yulimchen/vue3-h5-template/commit/93bbe723af5a54b0b11bf908b8366ca62267d0c9))

### [0.5.6](https://github.com/yulimchen/vue3-h5-template/compare/v0.5.5...v0.5.6) (2023-07-01)


### 💄 Styles | 风格

* **layout:** 修复大小写警告 ([53b1557](https://github.com/yulimchen/vue3-h5-template/commit/53b15578a55eccbae73377f27f7696b97087cf43))


### ✨ Features | 新功能

* 黑暗模式支持缓存 ([b398939](https://github.com/yulimchen/vue3-h5-template/commit/b398939ab7ba0c6b50ffb3a6b4f38ebc0512dc56))

### [0.5.5](https://github.com/yulimchen/vue3-h5-template/compare/v0.5.4...v0.5.5) (2023-06-30)


### ✏️ Documentation | 文档

* 文档更新 ([2663c8d](https://github.com/yulimchen/vue3-h5-template/commit/2663c8d2d5f1b7df92f38456e70a6dc60557e1cf))
* 文档更新 ([b89ce58](https://github.com/yulimchen/vue3-h5-template/commit/b89ce58729fcb7c90b6b295cbb90181e66f5c576))


### 🚀 Chore | 构建/工程依赖/工具

* **deps:** 部分依赖更新 ([5af0f5c](https://github.com/yulimchen/vue3-h5-template/commit/5af0f5c7466d5986a4a91cc07164c567dd082914))

### [0.5.4](https://github.com/yulimchen/vue3-h5-template/compare/v0.5.3...v0.5.4) (2023-04-25)


### 🚀 Chore | 构建/工程依赖/工具

* 添加 `standard-version` 管理版本 ([40aee75](https://github.com/yulimchen/vue3-h5-template/commit/40aee757f7364da04437b55861c585b999495abd))


### ✨ Features | 新功能

* **layout:** 支持深/浅色模式切换 ([5178735](https://github.com/yulimchen/vue3-h5-template/commit/51787354cf25fe173d4b122b517a6738fdc56693))

### [0.5.3](https://github.com/yulimchen/vue3-h5-template/compare/v0.5.2...v0.5.3) (2023-04-20)


### Bug Fixes

* **http:** 修复请求参数污染默认参数问题(fix [#28](https://github.com/yulimchen/vue3-h5-template/issues/28)) ([8795d91](https://github.com/yulimchen/vue3-h5-template/commit/8795d9138e9f42c52558804fc50d2aa8f06dad69))
* **Tabbar:** 底栏样式调整 ([fedb12d](https://github.com/yulimchen/vue3-h5-template/commit/fedb12d137005e17707444c5af41e9d296916da1))
* 修复 `postcss-mobile-forever` 插件导致颜色转换错误 (fix [#26](https://github.com/yulimchen/vue3-h5-template/issues/26)) ([5890ba1](https://github.com/yulimchen/vue3-h5-template/commit/5890ba11f441599976ba1f36008e5655fc558fd8))

### [0.5.2](https://github.com/yulimchen/vue3-h5-template/compare/v0.5.1...v0.5.2) (2023-03-06)


### Features

* 支持 `tailwindcss` & 重构页面样式 ([982f81b](https://github.com/yulimchen/vue3-h5-template/commit/982f81bc6e9f36da2d48f136eb5986b3f6f1310b))

### 0.5.1 (2023-02-20)


### Features

* `axios` 封装 ([224e1cd](https://github.com/yulimchen/vue3-h5-template/commit/224e1cd1325ebe7c250976c56c548a91d3bd644b))
* 按环境启用 `eruda` 调试工具 ([9be036d](https://github.com/yulimchen/vue3-h5-template/commit/9be036d84e6713ffcf4fc2c4b4991f0a4b3e86e3))
* 加入 `Pinia` 结合完成组件缓存 ([70b1ccf](https://github.com/yulimchen/vue3-h5-template/commit/70b1ccf15ca8d16611cb50e58b1ecde27a25c44a))
* 开发环境加入调试面板 ([fed71f5](https://github.com/yulimchen/vue3-h5-template/commit/fed71f58aae0ef13cda4e04b968144ac533b9acd))
* 全局a标签样式重置 ([878789a](https://github.com/yulimchen/vue3-h5-template/commit/878789a5b2698a8ee393983332da9829036f619b))
* 生产环境 `gzip` 压缩 ([ad884b9](https://github.com/yulimchen/vue3-h5-template/commit/ad884b9f8412b27562b47b1a254c8a57ef996e29))
* 生产环境开启gzip打包 ([c73a060](https://github.com/yulimchen/vue3-h5-template/commit/c73a0609fdf8c5285e3a0199fd7c3cc6531c2163))
* 视口 `vw/vh` 适配 ([fd66141](https://github.com/yulimchen/vue3-h5-template/commit/fd6614101648e05aad2f61aa4cad3649255df3f5))
* 首屏加载动画 ([d8dfa93](https://github.com/yulimchen/vue3-h5-template/commit/d8dfa933ae60c2031bca8ef3f453a842a107a60f))
* 添加 `svg-sprite` ([8bd1bab](https://github.com/yulimchen/vue3-h5-template/commit/8bd1bab01bfb6c846e6320577287f350e209e36b))
* 页面根据路由 `title` 设置标签名 ([caff118](https://github.com/yulimchen/vue3-h5-template/commit/caff118021eafbb8a911fd1ab1fab283baa06dcb))
* 增加vue-router ([2320803](https://github.com/yulimchen/vue3-h5-template/commit/23208030e6472bf9ee1de687fabbe1093ab03d08))
* 支持 svg 图标自动引入 ([e9c0e9b](https://github.com/yulimchen/vue3-h5-template/commit/e9c0e9bbf62ae9cff339f601f6db81032262bd04))
* 支持开发环境 `mock` ([f4626d0](https://github.com/yulimchen/vue3-h5-template/commit/f4626d088fd66ad076ac639b8d687d28f4f9dbcc))


### Bug Fixes

* **index.html:** 避免环境变量缺少导致页面错误 ([9584156](https://github.com/yulimchen/vue3-h5-template/commit/95841560ad5237bc35c6088c587cc105cc1dce5d))
* **SvgIcon:** 不解构使用props，避免数据失去reactive ([c6f3f07](https://github.com/yulimchen/vue3-h5-template/commit/c6f3f07303065054df4e1fc3d6b250edd4db00bf))
* **Tabbar:** 开启路由模式 ([1e61c5a](https://github.com/yulimchen/vue3-h5-template/commit/1e61c5a3555ced9bd6a5bf62302a2252bf01e12b))
