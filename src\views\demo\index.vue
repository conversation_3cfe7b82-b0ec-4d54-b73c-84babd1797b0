<script setup lang="ts">
import { reactive } from "vue";
import GridPatternDashed from "@/components/grid-pattern/grid-pattern-dashed.vue";

defineOptions({
  name: "Demo"
});

const contentList = reactive([
  { text: "⚡ Vue3 + Vite5", fullWidth: false },
  { text: "🍕 TypeScript", fullWidth: false },
  { text: "✨ Vant4 组件库", fullWidth: false },
  { text: "🍍 Pinia 状态管理", fullWidth: false },
  { text: "🗺️ Vue-router 4", fullWidth: false },
  { text: "👏 集成多种图标方案", fullWidth: false },
  { text: "🔧 集成 ESLint", fullWidth: false },
  { text: "🌓 支持深色模式", fullWidth: false },
  { text: "📏 vmin 视口适配", fullWidth: false },
  { text: "📡 Axios 封装", fullWidth: false },
  { text: "📦 打包资源 gzip 压缩", fullWidth: false },
  { text: "🛠️ 开发 Mock 数据", fullWidth: false },
  { text: "🚀 首屏加载动画", fullWidth: false },
  { text: "🔍 开发环境调试面板", fullWidth: false }
]);
</script>

<template>
  <GridPatternDashed />
  <div class="demo-content">
    <img class="logo" alt="Vue logo" src="~@/assets/logo_melomini.png" />
    <div class="info-card">
      <div>
        <a
          class="github-link"
          href="https://github.com/yulimchen/vue3-h5-template"
          target="_blank"
        >
          <svg-icon class="github-icon" name="github" />
          <h3 class="title">Vue3-h5-template</h3>
          <svg-icon class="link-icon" name="link" />
        </a>
      </div>
      <p class="description">
        🌱 基于 Vue3 全家桶、TypeScript、Vite 构建工具，开箱即用的 H5
        移动端项目基础模板
      </p>
    </div>

    <div class="features-grid">
      <div
        v-for="item in contentList"
        :key="item.text"
        :class="['feature-item', { 'full-width': item.fullWidth }]"
      >
        {{ item.text }}
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.demo-content {
  padding: 0 12px;
}

.logo {
  display: block;
  width: 120px;
  margin: 0 auto 30px;
  padding-top: 40px;
}

.info-card {
  font-size: 14px;
  padding: 12px 20px;
  border-radius: 12px;
  background: var(--color-block-background);
  margin-top: 14px;
}

.github-link {
  display: flex;
  align-items: center;
}

.github-icon {
  font-size: 20px;
  margin-right: 8px;
}

.title {
  font-weight: bold;
  font-size: 18px;
  margin: 4px 0;
}

.link-icon {
  font-size: 12px;
  margin-left: 5px;
}

.description {
  line-height: 24px;
  margin: 6px 0;
}

.features-grid {
  margin-top: 16px;
  padding-bottom: 24px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  font-size: 14px;
  text-align: center;
}

.feature-item {
  padding: 12px;
  border-radius: 12px;
  border: 1px solid var(--color-border);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  &.full-width {
    grid-column: span 2;
  }
}
</style>
